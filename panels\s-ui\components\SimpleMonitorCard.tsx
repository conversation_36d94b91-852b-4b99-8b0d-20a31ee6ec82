import { Badge } from '@/components/ui/badge';
import { Text } from '@/components/ui/text';
import { getUsageColor, monitorCardStyles } from '@/components/monitor/MonitorCardStyles';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig } from '@/lib/types';
import { ArrowDown, ArrowUp } from 'lucide-react-native';
import React from 'react';
import { View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { Pie, PolarChart } from 'victory-native';
import { SkeletonCard } from '~/components/SkeletonCard';

interface MonitorCardProps {
  config: SUIConfig;
  onPress?: (config: SUIConfig) => void;
  onLongPress?: (config: SUIConfig) => void;
}

// 动画饼图组件
interface AnimatedPieChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size: number;
  innerRadius?: number | string;
}

// 动画饼图切片组件
function AnimatedPieSlice({ slice }: { slice: any }) {
  return (
    <Pie.Slice
      animate={{
        type: "timing",
        duration: 500,
      }}
    />
  );
}

function AnimatedPieChart({ data, size, innerRadius = "85%" }: AnimatedPieChartProps) {
  return (
    <View style={{ width: size, height: size+5 }}>
      <PolarChart
        data={data}
        labelKey="label"
        valueKey="value"
        colorKey="color"
      >
        <Pie.Chart innerRadius={innerRadius}>
          {({ slice }) => <AnimatedPieSlice slice={slice} />}
        </Pie.Chart>
      </PolarChart>
    </View>
  );
}

export default function SimpleSUIMonitorCard({ config, onPress, onLongPress }: MonitorCardProps) {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  // 从全局状态中获取监控状态
  const { getMonitoringStatus } = useAppStore();
  const status = getMonitoringStatus(config.id);

  // 创建点击手势
  const tapGesture = Gesture.Tap().runOnJS(true)
    .onStart(() => {
      if (onPress) {
        onPress(config);
      }
    });

  // 创建长按手势
  const longPressGesture = Gesture.LongPress().runOnJS(true)
    .onStart(() => {
      if (onLongPress) {
        onLongPress(config);
      }
    });

  // 组合手势
  const combinedGesture = Gesture.Exclusive(longPressGesture, tapGesture);

  // 如果没有状态数据，显示骨架屏，但仍然支持手势
  if (!status) {
    return (
      <GestureDetector gesture={combinedGesture}>
        <SkeletonCard />
      </GestureDetector>
    );
  }

  // 格式化运行时间
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化网络速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };

  // 安全获取数值，确保为有效数字
  const safeNumber = (value: any): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
      return value;
    }
    return 0;
  };

  // 获取主机名
  const getHostname = (): string => {
    try {
      // 如果URL已经包含协议，直接使用
      if (config.url.startsWith('http://') || config.url.startsWith('https://')) {
        const url = new URL(config.url);
        return url.hostname;
      } else {
        // 如果URL不包含协议，添加协议后解析
        const url = new URL(`${config.protocol}://${config.url}`);
        return url.hostname;
      }
    } catch {
      // 如果解析失败，返回原始URL
      return config.url;
    }
  };

  return (
    <GestureDetector gesture={combinedGesture}>
      <View style={[monitorCardStyles.card, { backgroundColor, borderColor }]}>
        {/* 标题和状态 */}
        <View style={monitorCardStyles.header}>
          <View style={monitorCardStyles.titleContainer}>
            <View style={monitorCardStyles.titleRow}>
              <Text style={[monitorCardStyles.title, { color: textColor }]}>{config.name}</Text>
              <Badge variant="secondary" style={monitorCardStyles.ipBadge}>
                <Text style={monitorCardStyles.ipBadgeText}>{getHostname()}</Text>
              </Badge>
            </View>
          </View>
          <Badge
            variant={status.isOnline ? "default" : "destructive"}
            style={monitorCardStyles.statusBadge}
          >
            <Text style={monitorCardStyles.badgeText}>
              {status.isOnline
                ? status.serverStatus ? formatUptime(status.serverStatus.uptime) : 'Online'
                : 'Offline'
              }
            </Text>
          </Badge>
        </View>
            {/* 系统资源使用率圆环图 */}
            <View style={monitorCardStyles.chartsContainer}>
            <View style={monitorCardStyles.chartItem}>
              <View style={monitorCardStyles.pieContainer}>
                <AnimatedPieChart
                  data={status.serverStatus ? [
                    {
                      label: 'Used',
                      value: safeNumber(status.serverStatus.cpu),
                      color: getUsageColor(safeNumber(status.serverStatus.cpu))
                    },
                    {
                      label: 'Free',
                      value: 100 - safeNumber(status.serverStatus.cpu),
                      color: '#e5e7eb'
                    }
                  ] : [
                    {
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    },{
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    }
                  ]}
                  size={90}
                />
                <View style={monitorCardStyles.chartInnerContent}>
                  <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>CPU</Text>
                  <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                    {status.serverStatus ? `${safeNumber(status.serverStatus.cpu).toFixed(1)}%` : 'No data'}
                  </Text>
                </View>
              </View>
              <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                {status.serverStatus ? `${safeNumber(status.serverStatus.cpuCores)} cores` : 'No data'}
              </Text>
            </View>

            <View style={monitorCardStyles.chartItem}>
              <View style={monitorCardStyles.pieContainer}>
                <AnimatedPieChart
                  data={status.serverStatus ? (() => {
                    const memCurrent = safeNumber(status.serverStatus.mem.current);
                    const memTotal = safeNumber(status.serverStatus.mem.total);
                    const memPercentage = memTotal > 0 ? (memCurrent / memTotal) * 100 : 0;
                    return [
                      {
                        label: 'Used',
                        value: memPercentage,
                        color: getUsageColor(memPercentage)
                      },
                      {
                        label: 'Free',
                        value: 100 - memPercentage,
                        color: '#e5e7eb'
                      }
                    ];
                  })() : [
                    {
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    },{
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    }
                  ]}
                  size={90}
                />
                <View style={monitorCardStyles.chartInnerContent}>
                  <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>Memory</Text>
                  <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                    {status.serverStatus ? (() => {
                      const memCurrent = safeNumber(status.serverStatus.mem.current);
                      const memTotal = safeNumber(status.serverStatus.mem.total);
                      const memPercentage = memTotal > 0 ? (memCurrent / memTotal) * 100 : 0;
                      return `${memPercentage.toFixed(1)}%`;
                    })() : 'No data'}
                  </Text>
                </View>
              </View>
              <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                {status.serverStatus ? `${formatBytes(safeNumber(status.serverStatus.mem.current))}/${formatBytes(safeNumber(status.serverStatus.mem.total))}` : 'No data'}
              </Text>
            </View>

            <View style={monitorCardStyles.chartItem}>
              <View style={monitorCardStyles.pieContainer}>
                <AnimatedPieChart
                  data={status.serverStatus ? (() => {
                    const diskCurrent = safeNumber(status.serverStatus.disk.current);
                    const diskTotal = safeNumber(status.serverStatus.disk.total);
                    const diskPercentage = diskTotal > 0 ? (diskCurrent / diskTotal) * 100 : 0;
                    return [
                      {
                        label: 'Used',
                        value: diskPercentage,
                        color: getUsageColor(diskPercentage)
                      },
                      {
                        label: 'Free',
                        value: 100 - diskPercentage,
                        color: '#e5e7eb'
                      }
                    ];
                  })() : [
                    {
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    },{
                      label: 'No Data',
                      value: 100,
                      color: '#f3f4f6'
                    }
                  ]}
                  size={90}
                />
                <View style={monitorCardStyles.chartInnerContent}>
                  <Text style={[monitorCardStyles.chartInnerLabel, { color: textColor }]}>Disk</Text>
                  <Text style={[monitorCardStyles.percentageText, { color: textColor }]}>
                    {status.serverStatus ? (() => {
                      const diskCurrent = safeNumber(status.serverStatus.disk.current);
                      const diskTotal = safeNumber(status.serverStatus.disk.total);
                      const diskPercentage = diskTotal > 0 ? (diskCurrent / diskTotal) * 100 : 0;
                      return `${diskPercentage.toFixed(1)}%`;
                    })() : 'No data'}
                  </Text>
                </View>
              </View>
              <Text style={[monitorCardStyles.chartBottomLabel, { color: textColor }]}>
                {status.serverStatus ? `${formatBytes(safeNumber(status.serverStatus.disk.current))}/${formatBytes(safeNumber(status.serverStatus.disk.total))}` : 'No data'}
              </Text>
            </View>
          </View>

          {/* 网络统计 */}
          <View style={monitorCardStyles.networkStats}>
            {status.serverStatus ? (
              <View style={monitorCardStyles.networkTrafficRow}>
                <View style={monitorCardStyles.networkTrafficItem}>
                  <ArrowUp strokeWidth={3} size={14} color={textColor} style={monitorCardStyles.networkIcon} />
                  <Text style={[monitorCardStyles.networkTrafficText, { color: textColor }]}>
                    {formatSpeed(safeNumber(status.serverStatus.netIO.up))}    {formatBytes(safeNumber(status.serverStatus.netTraffic.sent))}
                  </Text>
                </View>
                <View style={monitorCardStyles.networkTrafficItem}>
                  <ArrowDown strokeWidth={3} size={14} color={textColor} style={monitorCardStyles.networkIcon} />
                  <Text style={[monitorCardStyles.networkTrafficText, { color: textColor }]}>
                    {formatSpeed(safeNumber(status.serverStatus.netIO.down))}    {formatBytes(safeNumber(status.serverStatus.netTraffic.recv))}
                  </Text>
                </View>
              </View>
            ) : (
              <View style={monitorCardStyles.networkTrafficRow}>
                <View style={monitorCardStyles.networkTrafficItem}>
                  <ArrowUp strokeWidth={3} size={14} color={textColor} style={monitorCardStyles.networkIcon} />
                  <Text style={[monitorCardStyles.networkTrafficText, { color: textColor }]}>No data</Text>
                </View>
                <View style={monitorCardStyles.networkTrafficItem}>
                  <ArrowDown strokeWidth={3} size={14} color={textColor} style={monitorCardStyles.networkIcon} />
                  <Text style={[monitorCardStyles.networkTrafficText, { color: textColor }]}>No data</Text>
                </View>
              </View>
            )}
          </View>
      </View>
    </GestureDetector>
  );
}
